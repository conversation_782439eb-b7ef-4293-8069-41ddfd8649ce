// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  inventoryItems InventoryItem[]
}

model Product {
  id           Int      @id @default(autoincrement())
  name         String   @unique
  category     String
  defaultUnit  String   @map("defaultunit")
  frequency    Int?     @default(0)

  // Relations
  inventoryItems InventoryItem[]

  @@index([name])
  @@index([category])
  @@index([frequency])
  @@map("products")
}

model InventoryItem {
  id         String   @id @default(cuid())
  productId  Int      @map("productid")
  weight     Float
  unit       String
  timestamp  DateTime @default(now())
  userId     String   @map("userid")
  createdAt  DateTime @default(now()) @map("createdat")
  updatedAt  DateTime @updatedAt @map("updatedat")

  // Relations
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Config {
  id    String @id @default(cuid())
  key   String @unique
  value String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model UserConfig {
  id                  String   @id @default(cuid())
  userId              String   @unique
  googleSheetsConfig String?  // JSON string
  useSharedSheet     Boolean  @default(false)
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
}

